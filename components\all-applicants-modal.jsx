"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { api } from "@/lib/api"
import { toast } from "sonner"
import useStore from "@/lib/store"
import {
  Loader2,
  Users,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  User,
  Building,
  Calendar,
  Star,
  Eye,
  CheckCircle,
  Clock,
  XCircle,
  ArrowLeft,
  UserCheck,
  TrendingUp,
  TrendingDown,
  Hash
} from "lucide-react"

export function AllApplicantsModal({ isOpen, onOpenChange, onRefreshCount }) {
  const { decrementApplicantCount } = useStore()
  const [applicants, setApplicants] = useState([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalApplicants, setTotalApplicants] = useState(0)
  const [perPage] = useState(12)

  // Filters
  const [statusFilter, setStatusFilter] = useState("all")
  const [nodeFilter, setNodeFilter] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")

  // Available nodes for filter (will be populated from applicants data)
  const [availableNodes, setAvailableNodes] = useState([])

  // New state for the redesigned modal
  const [view, setView] = useState('nodes') // 'nodes' or 'applicants' or 'detail'
  const [selectedNode, setSelectedNode] = useState(null)
  const [selectedApplicant, setSelectedApplicant] = useState(null)
  const [nodeApplicants, setNodeApplicants] = useState([])
  const [currentApplicantIndex, setCurrentApplicantIndex] = useState(0)
  const [groupedNodes, setGroupedNodes] = useState([])

  // Fetch applicants and group by nodes
  const fetchApplicants = async (page = 1) => {
    setLoading(true)
    try {
      const response = await api.network.getAllApplicants(
        perPage,
        page,
        statusFilter === "all" ? null : statusFilter,
        nodeFilter === "all" ? null : nodeFilter
      )

      if (response && response.data) {
        let applicantsData = response.data || []

        // Filter by search term if provided
        if (searchTerm.trim()) {
          applicantsData = applicantsData.filter(applicant => {
            const name = applicant.user?.name || applicant.user?.email || ''
            const nodeName = applicant.node?.label || applicant.node?.name || ''
            return name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   nodeName.toLowerCase().includes(searchTerm.toLowerCase())
          })
        }

        setApplicants(applicantsData)

        // Group applicants by nodes
        const nodeGroups = {}
        applicantsData.forEach(applicant => {
          if (applicant.node) {
            const nodeId = applicant.node.id
            if (!nodeGroups[nodeId]) {
              nodeGroups[nodeId] = {
                node: applicant.node,
                applicants: [],
                vacantCount: 0
              }
            }
            nodeGroups[nodeId].applicants.push(applicant)
            // Count vacant positions (status_id 3 means vacant)
            if (applicant.node.pointer_status_id === 3) {
              nodeGroups[nodeId].vacantCount++
            }
          }
        })

        setGroupedNodes(Object.values(nodeGroups))

        // Extract unique nodes for filter dropdown
        const nodes = [...new Map(
          applicantsData
            .filter(app => app.node)
            .map(app => [app.node.id, app.node])
        ).values()]
        setAvailableNodes(nodes)

        // Set pagination
        const meta = response.meta || {}
        setCurrentPage(meta.current_page || 1)
        setTotalPages(meta.last_page || 1)
        setTotalApplicants(meta.total || 0)
      }
    } catch (error) {
      console.error('Error fetching applicants:', error)
      toast.error('აპლიკანტების ჩატვირთვა ვერ მოხერხდა')
    } finally {
      setLoading(false)
    }
  }

  // Fetch applicants when modal opens or filters change
  useEffect(() => {
    if (isOpen) {
      setCurrentPage(1)
      fetchApplicants(1)
      setView('nodes') // Reset to nodes view when modal opens
    }
  }, [isOpen, statusFilter, nodeFilter])

  // Fetch when page changes
  useEffect(() => {
    if (isOpen && currentPage > 1) {
      fetchApplicants(currentPage)
    }
  }, [currentPage])

  // Search with debounce
  useEffect(() => {
    if (!isOpen) return

    const timeoutId = setTimeout(() => {
      setCurrentPage(1)
      fetchApplicants(1)
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  // Handle page change
  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page)
      fetchApplicants(page)
    }
  }

  // Handle node selection
  const handleNodeSelect = (nodeGroup) => {
    setSelectedNode(nodeGroup)
    setNodeApplicants(nodeGroup.applicants)
    setCurrentApplicantIndex(0)
    setView('applicants')
  }

  // Handle applicant detail view
  const handleApplicantDetail = (applicant) => {
    setSelectedApplicant(applicant)
    setView('detail')
  }

  // Handle back navigation
  const handleBack = () => {
    if (view === 'detail') {
      setView('applicants')
      setSelectedApplicant(null)
    } else if (view === 'applicants') {
      setView('nodes')
      setSelectedNode(null)
      setNodeApplicants([])
    }
  }

  // Navigate carousel
  const handlePrevApplicant = () => {
    setCurrentApplicantIndex(prev =>
      prev > 0 ? prev - 1 : nodeApplicants.length - 1
    )
  }

  const handleNextApplicant = () => {
    setCurrentApplicantIndex(prev =>
      prev < nodeApplicants.length - 1 ? prev + 1 : 0
    )
  }

  // Handle applicant confirmation
  const handleConfirmApplicant = async (applicant) => {
    try {
      await api.network.confirmApplicant(applicant.id)
      toast.success('აპლიკანტი წარმატებით დადასტურდა')

      // Decrement applicant count for the specific node
      if (applicant.node_position || applicant.node) {
        const nodePosition = applicant.node_position || applicant.node?.label
        const nodeId = applicant.node?.id
        decrementApplicantCount(nodePosition, nodeId)
      }

      fetchApplicants(currentPage) // Refresh data
      if (onRefreshCount) onRefreshCount()
    } catch (error) {
      console.error('Error confirming applicant:', error)
      toast.error('აპლიკანტის დადასტურება ვერ მოხერხდა')
    }
  }

  // Get status badge
  const getStatusBadge = (statusId) => {
    switch (statusId) {
      case 1:
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          <Clock className="w-3 h-3 mr-1" />
          ახალი
        </Badge>
      case 2:
        return <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          დადასტურებული
        </Badge>
      case 3:
        return <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
          <XCircle className="w-3 h-3 mr-1" />
          უარყოფილი
        </Badge>
      default:
        return <Badge variant="outline">უცნობი</Badge>
    }
  }

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('ka-GE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Clear filters
  const clearFilters = () => {
    setStatusFilter("all")
    setNodeFilter("all")
    setSearchTerm("")
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-full max-w-[95vw] h-[90vh] overflow-hidden flex flex-col bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900/20 dark:to-indigo-900/20">
        <DialogHeader className="pb-6 relative">
          {/* Back Button */}
          {view !== 'nodes' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="absolute left-0 top-0 z-10 hover:bg-white/80 dark:hover:bg-slate-800/80 backdrop-blur-sm"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              უკან
            </Button>
          )}

          <DialogTitle className="text-3xl font-bold text-center flex items-center justify-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
            {view === 'nodes' && `ყველა აპლიკანტი (${totalApplicants})`}
            {view === 'applicants' && `${selectedNode?.node?.label || selectedNode?.node?.name} - აპლიკანტები`}
            {view === 'detail' && 'აპლიკანტის დეტალური ინფორმაცია'}
          </DialogTitle>
        </DialogHeader>

        {/* Filters - Only show in nodes view */}
        {view === 'nodes' && (
          <div className="flex flex-col sm:flex-row gap-4 pb-6 border-b border-white/20 dark:border-slate-700/50">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="ძებნა სახელით ან ნოუდით..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30"
                />
              </div>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30">
                <SelectValue placeholder="სტატუსი" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">ყველა სტატუსი</SelectItem>
                <SelectItem value="1">ახალი</SelectItem>
                <SelectItem value="2">დადასტურებული</SelectItem>
                <SelectItem value="3">უარყოფილი</SelectItem>
              </SelectContent>
            </Select>

            <Select value={nodeFilter} onValueChange={setNodeFilter}>
              <SelectTrigger className="w-full sm:w-48 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30">
                <SelectValue placeholder="ნოუდი" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">ყველა ნოუდი</SelectItem>
                {availableNodes.map(node => (
                  <SelectItem key={node.id} value={node.id.toString()}>
                    {node.label || node.name || `ნოუდი ${node.id}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={clearFilters} className="whitespace-nowrap bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30 hover:bg-white dark:hover:bg-slate-700">
              <Filter className="w-4 h-4 mr-2" />
              გასუფთავება
            </Button>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <Loader2 className="w-8 h-8 animate-spin text-white" />
                </div>
                <p className="text-lg font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  მონაცემების ჩატვირთვა...
                </p>
              </div>
            </div>
          ) : (
            <>
              {/* Nodes View */}
              {view === 'nodes' && (
                <div className="h-full overflow-y-auto">
                  {groupedNodes.length === 0 ? (
                    <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                      <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500/20 to-purple-600/20 flex items-center justify-center">
                        <Users className="w-12 h-12 opacity-50" />
                      </div>
                      <p className="text-xl font-medium">აპლიკანტები არ მოიძებნა</p>
                      <p className="text-sm mt-2">შეცვალეთ ფილტრები ან სცადეთ სხვა ძებნის ტერმინი</p>
                    </div>
                  ) : (
                    <div className="p-6 space-y-4">
                      {groupedNodes.map((nodeGroup, index) => (
                        <Card
                          key={nodeGroup.node.id}
                          className="group cursor-pointer hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30 hover:scale-[1.02] hover:bg-white dark:hover:bg-slate-800"
                          onClick={() => handleNodeSelect(nodeGroup)}
                        >
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                                  <Building className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                  <h3 className="text-lg font-semibold text-foreground group-hover:text-blue-600 transition-colors">
                                    {nodeGroup.node.label || nodeGroup.node.name || `ნოუდი ${nodeGroup.node.id}`}
                                  </h3>
                                  <div className="flex items-center gap-4 mt-1">
                                    <span className="text-sm text-muted-foreground flex items-center gap-1">
                                      <Users className="w-4 h-4" />
                                      {nodeGroup.applicants.length} აპლიკანტი
                                    </span>
                                    {nodeGroup.node.pointer_status_id === 3 && (
                                      <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                        ვაკანტური
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-blue-600 transition-colors" />
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Applicants Carousel View */}
              {view === 'applicants' && nodeApplicants.length > 0 && (
                <div className="h-full flex flex-col">
                  {/* Carousel Navigation */}
                  <div className="flex items-center justify-between p-4 bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border-b border-white/20 dark:border-slate-700/50">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handlePrevApplicant}
                      disabled={nodeApplicants.length <= 1}
                      className="hover:bg-white/80 dark:hover:bg-slate-700/80"
                    >
                      <ChevronLeft className="w-4 h-4 mr-2" />
                      წინა
                    </Button>

                    <div className="text-sm text-muted-foreground">
                      {currentApplicantIndex + 1} / {nodeApplicants.length}
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleNextApplicant}
                      disabled={nodeApplicants.length <= 1}
                      className="hover:bg-white/80 dark:hover:bg-slate-700/80"
                    >
                      შემდეგი
                      <ChevronRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>

                  {/* Current Applicant Card */}
                  <div className="flex-1 flex items-center justify-center p-6">
                    {nodeApplicants[currentApplicantIndex] && (
                      <Card className="w-full max-w-md bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-white/30 dark:border-slate-600/30 shadow-2xl">
                        <CardContent className="p-8 text-center">
                          <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                            <User className="w-10 h-10 text-white" />
                          </div>

                          <h3 className="text-xl font-bold mb-2">
                            მომხმარებელი #{nodeApplicants[currentApplicantIndex].user?.id || 'N/A'}
                          </h3>

                          <div className="space-y-3 mb-6 text-sm">
                            <div className="flex items-center justify-center gap-2">
                              <Star className="w-4 h-4 text-yellow-500" />
                              <span>რეიტინგი: {nodeApplicants[currentApplicantIndex].user?.rate || 'N/A'}/10</span>
                            </div>

                            <div className="flex items-center justify-center gap-2">
                              <TrendingDown className="w-4 h-4 text-red-500" />
                              <span>დატოვების რაოდენობა: {nodeApplicants[currentApplicantIndex].user?.leave_count || 0}</span>
                            </div>

                            <div className="flex items-center justify-center gap-2">
                              <XCircle className="w-4 h-4 text-red-500" />
                              <span>გაგდების რაოდენობა: {nodeApplicants[currentApplicantIndex].user?.fire_count || 0}</span>
                            </div>

                            <div className="flex items-center justify-center gap-2">
                              <Calendar className="w-4 h-4 text-blue-500" />
                              <span>{formatDate(nodeApplicants[currentApplicantIndex].created_at)}</span>
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-3">
                            <Button
                              onClick={() => handleApplicantDetail(nodeApplicants[currentApplicantIndex])}
                              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                            >
                              <Eye className="w-4 h-4 mr-2" />
                              დეტალური ნახვა
                            </Button>

                            <Button
                              onClick={() => handleConfirmApplicant(nodeApplicants[currentApplicantIndex])}
                              variant="outline"
                              className="w-full border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
                            >
                              <UserCheck className="w-4 h-4 mr-2" />
                              დადასტურება
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>

              {/* Detail View */}
              {view === 'detail' && selectedApplicant && (
                <div className="h-full overflow-y-auto p-6">
                  <Card className="max-w-2xl mx-auto bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-white/30 dark:border-slate-600/30 shadow-2xl">
                    <CardContent className="p-8">
                      <div className="text-center mb-8">
                        <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                          <User className="w-12 h-12 text-white" />
                        </div>
                        <h2 className="text-2xl font-bold mb-2">
                          {selectedApplicant.user?.name || selectedApplicant.user?.email || 'უცნობი მომხმარებელი'}
                        </h2>
                        <div className="flex items-center justify-center gap-2">
                          <Hash className="w-4 h-4 text-muted-foreground" />
                          <span className="text-muted-foreground">ID: {selectedApplicant.user?.id || 'N/A'}</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div className="space-y-4">
                          <div className="p-4 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800">
                            <div className="flex items-center gap-2 mb-2">
                              <Star className="w-5 h-5 text-yellow-500" />
                              <span className="font-semibold">რეიტინგი</span>
                            </div>
                            <p className="text-2xl font-bold text-blue-600">
                              {selectedApplicant.user?.rate || 'N/A'}/10
                            </p>
                          </div>

                          <div className="p-4 rounded-lg bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200 dark:border-red-800">
                            <div className="flex items-center gap-2 mb-2">
                              <TrendingDown className="w-5 h-5 text-red-500" />
                              <span className="font-semibold">დატოვების რაოდენობა</span>
                            </div>
                            <p className="text-2xl font-bold text-red-600">
                              {selectedApplicant.user?.leave_count || 0}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div className="p-4 rounded-lg bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800">
                            <div className="flex items-center gap-2 mb-2">
                              <XCircle className="w-5 h-5 text-red-500" />
                              <span className="font-semibold">გაგდების რაოდენობა</span>
                            </div>
                            <p className="text-2xl font-bold text-red-600">
                              {selectedApplicant.user?.fire_count || 0}
                            </p>
                          </div>

                          <div className="p-4 rounded-lg bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                            <div className="flex items-center gap-2 mb-2">
                              <Calendar className="w-5 h-5 text-green-500" />
                              <span className="font-semibold">განაცხადის თარიღი</span>
                            </div>
                            <p className="text-sm font-medium text-green-600">
                              {formatDate(selectedApplicant.created_at)}
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-4">
                        <Button
                          onClick={() => handleConfirmApplicant(selectedApplicant)}
                          className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                        >
                          <UserCheck className="w-4 h-4 mr-2" />
                          დადასტურება
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}

        {/* Pagination - Only show in nodes view */}
        {view === 'nodes' && totalPages > 1 && (
          <div className="flex items-center justify-between pt-6 border-t border-white/20 dark:border-slate-700/50">
            <div className="text-sm text-muted-foreground">
              გვერდი {currentPage} / {totalPages} (სულ {totalApplicants})
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || loading}
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30 hover:bg-white dark:hover:bg-slate-700"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              <span className="px-3 py-1 text-sm bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-white/30 dark:border-slate-600/30 rounded">
                {currentPage}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || loading}
                className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-white/30 dark:border-slate-600/30 hover:bg-white dark:hover:bg-slate-700"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
